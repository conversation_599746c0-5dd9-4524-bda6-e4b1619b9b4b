#include "waveform_display.h"

#define WAVEFORM_WIDTH  (lcddev.width - 40)

void generate_sine_wave(uint16_t *buffer, uint16_t points, float amplitude, float offset) {
    for(uint16_t i = 0; i < points; i++) {
        float angle = 2 * M_PI * i / points;
        buffer[i] = (uint16_t)(amplitude * sinf(angle) + offset);
    }
}

void generate_triangle_wave(uint16_t *buffer, uint16_t points, float amplitude, float offset) {
    for(uint16_t i = 0; i < points; i++) {
        float value;
        if(i < points/4) {
            value = 4.0f * i / points;
        } else if(i < 3*points/4) {
            value = 2.0f - 4.0f * i / points;
        } else {
            value = -4.0f + 4.0f * i / points;
        }
        buffer[i] = (uint16_t)(amplitude * value + offset);
    }
}

void draw_waveform(uint16_t *waveform, uint16_t points, uint16_t color, uint16_t offset_y) {
    // Draw axes
    lcd_draw_hline(WAVEFORM_OFFSET_X, offset_y + WAVEFORM_HEIGHT/2, WAVEFORM_WIDTH, BLACK);
    // Draw vertical axis using points
    for(uint16_t y = offset_y; y < offset_y + WAVEFORM_HEIGHT; y++) {
        lcd_draw_point(WAVEFORM_OFFSET_X, y, BLACK);
    }
    
    // Draw waveform
    for(uint16_t i = 0; i < points - 1; i++) {
        uint16_t x1 = WAVEFORM_OFFSET_X + i * WAVEFORM_WIDTH / points;
        uint16_t y1 = offset_y + WAVEFORM_HEIGHT/2 - waveform[i] * WAVEFORM_HEIGHT / 4096;
        uint16_t x2 = WAVEFORM_OFFSET_X + (i+1) * WAVEFORM_WIDTH / points;
        uint16_t y2 = offset_y + WAVEFORM_HEIGHT/2 - waveform[i+1] * WAVEFORM_HEIGHT / 4096;
        
        lcd_draw_line(x1, y1, x2, y2, color);
    }
    
    // Label the waveform
    lcd_show_string(WAVEFORM_OFFSET_X, offset_y - 20, 100, 16, 16, "Waveform", BLACK);
}
