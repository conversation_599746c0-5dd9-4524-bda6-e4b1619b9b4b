#include "stm32f4xx.h"
#include "stdio.h"
#include "usart.h"
#include "delay.h"
#include "led.h"
#include "G.h"
#include "timer.h"
#include "math.h"
#include "arm_math.h"
#include "kalman.h"
#include "fft.h"
#include "adc.h"
#include "AD9833.h"
#include "lcd.h"
#include "waveform_display.h"
#include <stdbool.h>
#include <string.h>
#include <stdlib.h>

// Function prototypes match the declarations in adc.h
void QCZ_FFT(volatile uint16_t* buff);
void QCZ_FFT1(volatile uint16_t* buff);

// Global variables from your project
bool Separate = false;
extern int t;
extern float fft_outputbuf[FFT_LENGTH];
extern u8 Res;
uint32_t frequency_A, frequency_B;
int phase_difference_A;
int phase_difference_B;
int phase_difference_A1;
int phase_difference_B1;
extern float phase_A, phase_B, phase;
extern float frequency;
double current_output_freq_A, current_output_freq_B;
float phase_A_CS = 0.0f;
float phase_B_CS = 0.0f;
float phase_A_SX = 0.0f;
float phase_B_SX = 0.0f;
uint16_t current_phase_B = 0;
uint32_t peak_idx;
extern uint32_t peak1_idx, peak2_idx;

// Extern declarations now match the original types in your header files
extern volatile uint16_t buff_adc[];
extern volatile uint16_t buff_adc2[];
extern volatile uint16_t buff_adc3[];

// CORRECTED: Declarations are now on separate lines to match adc.h
extern volatile u8 flag_ADC;
extern volatile u8 flag_ADC1;
extern volatile u8 flag_ADC2;

extern float sampfre;
extern arm_cfft_radix4_instance_f32 scfft;

u8 QCZ = 100;
u8 QCZ1 = 0;
int QCZ_Phase[2];
int QCZ_Phase1[2];
int Phase = 0;
float ZE;
int SBP = 0;

uint16_t waveform_A, waveform_B;
uint16_t waveform_A_prime, waveform_B_prime;

char lcd_buffer[50];
uint16_t waveform_data_A[WAVEFORM_POINTS];
uint16_t waveform_data_B[WAVEFORM_POINTS];


// NEW HELPER FUNCTION: Converts waveform define to a string for printing
const char* get_wave_str(uint16_t wave_const)
{
    if (wave_const == AD9833_OUT_SINUS || wave_const == AD9833_OUT_SINUS1)
    {
        return "Sine";
    }
    else if (wave_const == AD9833_OUT_TRIANGLE || wave_const == AD9833_OUT_TRIANGLE1)
    {
        return "Triangle";
    }
    return "Sine";
}


int main(void)
{
    arm_cfft_radix4_init_f32(&scfft, FFT_LENGTH, 0, 1);
    NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);
    uart_init(112500);
    delay_init(168);
    LED_Init();
    Adc_Init();
    Adc2_Init();
    Adc3_Init();
    DMA1_Init();
    DMA2_Init();
    DMA3_Init();
    AD9833_Init();
    AD9833_Init1();

    lcd_init();

    sampfre = 409756;

    TIM3_Int_Init(5 - 1, 41 - 1);
    TIM4_Int_Init(1000 - 1, 8400 - 1);
    TIM_Cmd(TIM3, ENABLE);

    // UI Redesign for better aesthetics and clarity
    lcd_clear(WHITE);
    g_point_color = BLACK;

    const uint8_t FONT_SIZE = 16;
    const uint8_t LINE_HEIGHT = FONT_SIZE + 6;
    const uint16_t MARGIN = 10;
    const uint16_t PANEL_WIDTH = lcddev.width - 2 * MARGIN;

    const uint16_t COL1_X = 10;
    const uint16_t COL2_X = 70;
    const uint16_t COL3_X = 200;

    uint16_t y_pos = MARGIN;

    // Panel 1: Target Generator Setup
    lcd_fill(MARGIN, y_pos, MARGIN + PANEL_WIDTH, y_pos + LINE_HEIGHT, BLUE);
    lcd_show_string(MARGIN + 5, y_pos + 3, PANEL_WIDTH, LINE_HEIGHT, FONT_SIZE, "Target Generator Setup", BLACK);
    y_pos += LINE_HEIGHT;
    lcd_draw_rectangle(MARGIN, y_pos, MARGIN + PANEL_WIDTH, y_pos + (3 * LINE_HEIGHT), BLUE);
    y_pos += 5;
    lcd_show_string(MARGIN + COL1_X, y_pos, 200, FONT_SIZE, FONT_SIZE, "CH", BLUE);
    lcd_show_string(MARGIN + COL2_X, y_pos, 200, FONT_SIZE, FONT_SIZE, "Frequency (Hz)", BLUE);
    lcd_show_string(MARGIN + COL3_X, y_pos, 200, FONT_SIZE, FONT_SIZE, "Waveform", BLUE);
    uint16_t panel1_data_y_start = y_pos + LINE_HEIGHT;
    y_pos += 3 * LINE_HEIGHT;


    // Panel 2: Feedback & PLL Status
    y_pos += MARGIN;
    uint16_t panel2_start_y = y_pos;
    lcd_fill(MARGIN, y_pos, MARGIN + PANEL_WIDTH, y_pos + LINE_HEIGHT, BLUE);
    lcd_show_string(MARGIN + 5, y_pos + 3, PANEL_WIDTH, LINE_HEIGHT, FONT_SIZE, "Feedback & PLL Status", BLACK);
    y_pos += LINE_HEIGHT;
    lcd_draw_rectangle(MARGIN, y_pos, MARGIN + PANEL_WIDTH, y_pos + (4 * LINE_HEIGHT), BLUE);
    y_pos += 5;
    lcd_show_string(MARGIN + COL1_X, y_pos, 200, FONT_SIZE, FONT_SIZE, "CH", BLUE);
    lcd_show_string(MARGIN + COL2_X, y_pos, 200, FONT_SIZE, FONT_SIZE, "Measured Freq", BLUE);
    lcd_show_string(MARGIN + COL3_X, y_pos, 200, FONT_SIZE, FONT_SIZE, "Waveform", BLUE);
    uint16_t panel2_data_y_start = y_pos + LINE_HEIGHT;

    uint16_t lock_status_y = panel2_data_y_start + 2 * LINE_HEIGHT;

    const double FREQUENCY_LOCK_TOLERANCE = 10.0;
    AD9833_SetPhase1(AD9833_REG1_PHASE0, current_phase_B);

    while (1)
    {
        if(GPIO_ReadInputDataBit(GPIOE, GPIO_Pin_4) == 0) {
            delay_ms(50);
            if(GPIO_ReadInputDataBit(GPIOE, GPIO_Pin_4) == 0) {
                current_phase_B = (current_phase_B + 228) % 4096;
                AD9833_SetPhase1(AD9833_REG1_PHASE0, current_phase_B);
                while(GPIO_ReadInputDataBit(GPIOE, GPIO_Pin_4) == 0);
            }
        }

        while (flag_ADC == 0 || flag_ADC1 == 0 || flag_ADC2 == 0) {}

        QCZ_FFT(buff_adc);

        if (fft_outputbuf[peak1_idx] < 500 && fft_outputbuf[peak1_idx] > 430) {
            waveform_A = AD9833_OUT_SINUS;
        } else if (fft_outputbuf[peak1_idx] < 420) {
            waveform_A = AD9833_OUT_TRIANGLE;
        }

        if (fft_outputbuf[peak2_idx] < 500 && fft_outputbuf[peak2_idx] > 430) {
            waveform_B = AD9833_OUT_SINUS1;
        } else if (fft_outputbuf[peak2_idx] < 420) {
            waveform_B = AD9833_OUT_TRIANGLE1;
        }

        frequency_A = peak1_idx * 100;
        frequency_B = peak2_idx * 100;

        AD9833_SetFrequencyQuick(frequency_A, waveform_A);
        AD9833_SetFrequencyQuick1(frequency_B, waveform_B);

        current_output_freq_A = (double)frequency_A;
        current_output_freq_B = (double)frequency_B;

        lcd_fill(MARGIN + 1, panel1_data_y_start, MARGIN + PANEL_WIDTH - 1, panel1_data_y_start + 2 * LINE_HEIGHT - 5, WHITE);

        lcd_show_string(MARGIN + COL1_X, panel1_data_y_start, 200, FONT_SIZE, FONT_SIZE, "A", g_point_color);
        sprintf(lcd_buffer, "%lu", frequency_A);
        lcd_show_string(MARGIN + COL2_X, panel1_data_y_start, 200, FONT_SIZE, FONT_SIZE, lcd_buffer, g_point_color);
        lcd_show_string(MARGIN + COL3_X, panel1_data_y_start, 200, FONT_SIZE, FONT_SIZE, (char*)get_wave_str(waveform_A), g_point_color);

        lcd_show_string(MARGIN + COL1_X, panel1_data_y_start + LINE_HEIGHT, 200, FONT_SIZE, FONT_SIZE, "B", g_point_color);
        sprintf(lcd_buffer, "%lu", frequency_B);
        lcd_show_string(MARGIN + COL2_X, panel1_data_y_start + LINE_HEIGHT, 200, FONT_SIZE, FONT_SIZE, lcd_buffer, g_point_color);
        lcd_show_string(MARGIN + COL3_X, panel1_data_y_start + LINE_HEIGHT, 200, FONT_SIZE, FONT_SIZE, (char*)get_wave_str(waveform_B), g_point_color);

        // Generate and display waveforms
        // Generate waveform A based on A' actual waveform
        if(waveform_A_prime == AD9833_OUT_SINUS) {
            generate_sine_wave(waveform_data_A, WAVEFORM_POINTS, 2048, 2048);
        } else {
            generate_triangle_wave(waveform_data_A, WAVEFORM_POINTS, 2048, 2048);
        }
        
        // Generate waveform B based on B' actual waveform
        if(waveform_B_prime == AD9833_OUT_SINUS1) {
            generate_sine_wave(waveform_data_B, WAVEFORM_POINTS, 2048, 2048);
        } else {
            generate_triangle_wave(waveform_data_B, WAVEFORM_POINTS, 2048, 2048);
        }
        
        draw_waveform(waveform_data_A, WAVEFORM_POINTS, RED, WAVEFORM_OFFSET_Y_A);
        draw_waveform(waveform_data_B, WAVEFORM_POINTS, BLUE, WAVEFORM_OFFSET_Y_B);

        delay_ms(100);

        flag_ADC = 0; flag_ADC1 = 0; flag_ADC2 = 0;
        TIM_Cmd(TIM3, ENABLE);
        while (flag_ADC == 0 || flag_ADC1 == 0 || flag_ADC2 == 0) {}

        QCZ_FFT(buff_adc);
        phase_A_CS = phase_A;
        phase_B_CS = phase_B;
        if (peak1_idx > peak2_idx) {
            peak_idx = peak1_idx; peak1_idx = peak2_idx; peak2_idx = peak_idx;
            phase_A_CS = phase_B; phase_B_CS = phase_A;
        }

        QCZ_FFT1(buff_adc2);
        phase_A_SX = phase;
        double measured_freq_A_feedback = frequency;
        if (fft_outputbuf[peak_idx] > 430) {
            waveform_A_prime = AD9833_OUT_SINUS;
        } else {
            waveform_A_prime = AD9833_OUT_TRIANGLE;
        }

        QCZ_FFT1(buff_adc3);
        phase_B_SX = phase;
        double measured_freq_B_feedback = frequency;
        if (fft_outputbuf[peak_idx] > 430) {
            waveform_B_prime = AD9833_OUT_SINUS1;
        } else {
            waveform_B_prime = AD9833_OUT_TRIANGLE1;
        }

        phase_difference_A1 = phase_A_CS - phase_A_SX;
        if (phase_difference_A1 > 180) phase_difference_A1 -= 180;
        if (phase_difference_A1 < -180) phase_difference_A1 += 180;
        if (phase_difference_A1 < 0) phase_difference_A1 = -phase_difference_A1;
        if (phase_difference_A1 > 100) phase_difference_A1 = 180 - phase_difference_A1;

        phase_difference_B1 = phase_B_CS - phase_B_SX;
        if (phase_difference_B1 > 180) phase_difference_B1 -= 180;
        if (phase_difference_B1 < -180) phase_difference_B1 += 180;
        if (phase_difference_B1 < 0) phase_difference_B1 = -phase_difference_B1;
        if (phase_difference_B1 > 100) phase_difference_B1 = 180 - phase_difference_B1;

        if (fabs(measured_freq_A_feedback - frequency_A) < FREQUENCY_LOCK_TOLERANCE &&
            fabs(measured_freq_B_feedback - frequency_B) < FREQUENCY_LOCK_TOLERANCE)
        {
            Separate = true;
            Phase = -1;
            ZE = (double)frequency_B / frequency_A;
        } else {
            Separate = false;
        }

        while (Separate)
        {
            if (Phase == -1) {
                QCZ_Phase[0] = phase_difference_A1;
                QCZ_Phase1[0] = phase_difference_B1;
            } else {
                QCZ_Phase[0] = phase_difference_A1;
                QCZ_Phase1[0] = phase_difference_B1;
            }
            Phase++;

            flag_ADC = 0; flag_ADC1 = 0; flag_ADC2 = 0;
            TIM_Cmd(TIM3, ENABLE);
            while (flag_ADC == 0 || flag_ADC1 == 0 || flag_ADC2 == 0) {}

            QCZ_FFT(buff_adc);
            phase_A_CS = phase_A;
            phase_B_CS = phase_B;
            if (peak1_idx > peak2_idx) {
                peak_idx = peak1_idx; peak1_idx = peak2_idx; peak2_idx = peak_idx;
                phase_A_CS = phase_B; phase_B_CS = phase_A;
            }

            QCZ_FFT1(buff_adc2);
            phase_A_SX = phase;
            measured_freq_A_feedback = frequency;
            if (fft_outputbuf[peak_idx] > 430) waveform_A_prime = AD9833_OUT_SINUS;
            else waveform_A_prime = AD9833_OUT_TRIANGLE;

            phase_difference_A1 = phase_A_CS - phase_A_SX;
            if (phase_difference_A1 > 180) phase_difference_A1 -= 180;
            if (phase_difference_A1 < -180) phase_difference_A1 += 180;
            if (phase_difference_A1 < 0) phase_difference_A1 = -phase_difference_A1;
            if (phase_difference_A1 > 100) phase_difference_A1 = 180 - phase_difference_A1;

            QCZ_FFT1(buff_adc3);
            phase_B_SX = phase;
            measured_freq_B_feedback = frequency;
            if (fft_outputbuf[peak_idx] > 430) waveform_B_prime = AD9833_OUT_SINUS1;
            else waveform_B_prime = AD9833_OUT_TRIANGLE1;

            phase_difference_B1 = phase_B_CS - phase_B_SX;
            if (phase_difference_B1 > 180) phase_difference_B1 -= 180;
            if (phase_difference_B1 < -180) phase_difference_B1 += 180;
            if (phase_difference_B1 < 0) phase_difference_B1 = -phase_difference_B1;
            if (phase_difference_B1 > 100) phase_difference_B1 = 180 - phase_difference_B1;

            QCZ_Phase[1] = phase_difference_A1;
            QCZ_Phase1[1] = phase_difference_B1;

            static float integral_A = 0.0f;
            float phase_error_A = fmod(QCZ_Phase[0] - QCZ_Phase[1] + 180.0f, 360.0f) - 180.0f;
            float freq_adjust_A = phase_error_A * 0.005f + integral_A * 0.0005f;
            current_output_freq_A += freq_adjust_A;
            AD9833_SetFrequencyQuick(current_output_freq_A, waveform_A);
            integral_A += phase_error_A * 0.1f;
            if(fabs(integral_A) > 30.0f) integral_A = 30.0f * (integral_A > 0 ? 1 : -1);

            static float integral_B = 0.0f;
            float phase_error_B = fmod(QCZ_Phase1[0] - QCZ_Phase1[1] + 180.0f, 360.0f) - 180.0f;
            float freq_adjust_B = phase_error_B * 0.005f + integral_B * 0.0005f;
            current_output_freq_B += freq_adjust_B;
            AD9833_SetFrequencyQuick1(current_output_freq_B, waveform_B);
            integral_B += phase_error_B * 0.1f;
            if(fabs(integral_B) > 30.0f) integral_B = 30.0f * (integral_B > 0 ? 1 : -1);

            static int lock_count_A = 0;
            static int lock_count_B = 0;
            bool is_locked_A = fabs(phase_error_A) < 8.0f && fabs(freq_adjust_A) < 0.2f;
            bool is_locked_B = fabs(phase_error_B) < 8.0f && fabs(freq_adjust_B) < 0.2f;
            lock_count_A = is_locked_A ? (lock_count_A + 1) : 0;
            lock_count_B = is_locked_B ? (lock_count_B + 1) : 0;

            lcd_fill(MARGIN + 1, panel2_data_y_start, MARGIN + PANEL_WIDTH - 1, panel2_start_y + (4 * LINE_HEIGHT) -1, WHITE);

            lcd_show_string(MARGIN + COL1_X, panel2_data_y_start, 200, FONT_SIZE, FONT_SIZE, "A'", g_point_color);
            sprintf(lcd_buffer, "%.2f", measured_freq_A_feedback);
            lcd_show_string(MARGIN + COL2_X, panel2_data_y_start, 200, FONT_SIZE, FONT_SIZE, lcd_buffer, g_point_color);
            // CORRECTED: Typo FENT_SIZE is now FONT_SIZE
            lcd_show_string(MARGIN + COL3_X, panel2_data_y_start, 200, FONT_SIZE, FONT_SIZE, (char*)get_wave_str(waveform_A), g_point_color);

            lcd_show_string(MARGIN + COL1_X, panel2_data_y_start + LINE_HEIGHT, 200, FONT_SIZE, FONT_SIZE, "B'", g_point_color);
            sprintf(lcd_buffer, "%.2f", measured_freq_B_feedback);
            lcd_show_string(MARGIN + COL2_X, panel2_data_y_start + LINE_HEIGHT, 200, FONT_SIZE, FONT_SIZE, lcd_buffer, g_point_color);
            lcd_show_string(MARGIN + COL3_X, panel2_data_y_start + LINE_HEIGHT, 200, FONT_SIZE, FONT_SIZE, (char*)get_wave_str(waveform_B), g_point_color);

            sprintf(lcd_buffer, "A Lock: %s", (lock_count_A > 5) ? "LOCKED" : "Searching...");
            lcd_show_string(MARGIN + COL1_X, lock_status_y, 200, FONT_SIZE, FONT_SIZE, lcd_buffer, (lock_count_A > 5) ? GREEN : ORANGE);

            sprintf(lcd_buffer, "B Lock: %s", (lock_count_B > 5) ? "LOCKED" : "Searching...");
            lcd_show_string(MARGIN + COL1_X, lock_status_y + LINE_HEIGHT, 200, FONT_SIZE, FONT_SIZE, lcd_buffer, (lock_count_B > 5) ? GREEN : ORANGE);

            if (fabs(measured_freq_A_feedback - frequency_A) > FREQUENCY_LOCK_TOLERANCE ||
                fabs(measured_freq_B_feedback - frequency_B) > FREQUENCY_LOCK_TOLERANCE)
            {
                Separate = false;
            }

            flag_ADC = 0; flag_ADC1 = 0; flag_ADC2 = 0;
            TIM_Cmd(TIM3, ENABLE);
        }

        flag_ADC = 0; flag_ADC1 = 0; flag_ADC2 = 0;
        TIM_Cmd(TIM3, ENABLE);
    }
}
