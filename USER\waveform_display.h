#ifndef __WAVEFORM_DISPLAY_H
#define __WAVEFORM_DISPLAY_H

#include "stm32f4xx.h"
#include "lcd.h"
#include <math.h>

#define M_PI 3.14159265358979323846
#define WAVEFORM_POINTS 100
#define WAVEFORM_HEIGHT 100
#define WAVEFORM_OFFSET_X 20
#define WAVEFORM_OFFSET_Y_A 310
#define WAVEFORM_OFFSET_Y_B 460

void generate_sine_wave(uint16_t *buffer, uint16_t points, float amplitude, float offset);
void generate_triangle_wave(uint16_t *buffer, uint16_t points, float amplitude, float offset);
void draw_waveform(uint16_t *waveform, uint16_t points, uint16_t color, uint16_t offset_y);

#endif
